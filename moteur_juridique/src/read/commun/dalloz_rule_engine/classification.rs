use crate::dalloz_types::Classification;
use crate::read::commun::classification_contrat::ClassificationContrat;
use crate::read::commun::dalloz_rule_engine::rule::Rule;

impl Rule for Option<Classification> {
    type InputType = ClassificationContrat;

    fn is_applicable(&self, item: &Self::InputType) -> bool {
        if self.is_none() {
            return true;
        }

        let classification = self.as_ref().unwrap();

        if item.is_none() {
            return false;
        }

        let item = item.as_ref().unwrap();

        if !classification.niveau.is_applicable(&item.niveau) {
            return false;
        }
        if !classification.position.is_applicable(&item.position) {
            return false;
        }
        if !classification.echelon.is_applicable(&item.echelon) {
            return false;
        }
        if !classification.categorie.is_applicable(&item.categorie) {
            return false;
        }
        if !classification.coefficient.is_applicable(&item.coefficient) {
            return false;
        }
        if !classification.indice.is_applicable(&item.indice) {
            return false;
        }
        if !classification.degre.is_applicable(&item.degre) {
            return false;
        }
        if !classification
            .qualification
            .is_applicable(&item.qualification)
        {
            return false;
        }
        if !classification.classe.is_applicable(&item.classe) {
            return false;
        }
        if !classification.palier.is_applicable(&item.palier) {
            return false;
        }
        if !classification.points.is_applicable(&item.points) {
            return false;
        }
        if !classification.seuil.is_applicable(&item.seuil) {
            return false;
        }
        if !classification.strate.is_applicable(&item.strate) {
            return false;
        }

        true
    }
}

#[cfg(test)]
mod tests {
    use super::Rule;
    use crate::dalloz_structure::{Classification, ItemInclus};
    use crate::read::commun::classification_contrat::ClassificationContrat;

    #[test]
    fn it_should_return_true_for_empty_list() {
        // Given I have an empty list
        let list: Option<Classification> = None;

        // When I check if an item is applicable
        let result = list.is_applicable(&None);

        assert_eq!(result, true);
    }

    #[test]
    fn it_should_match_the_classification_when_applicable() {
        // Given I have a list with inclusion items
        let list = Some(Classification {
            niveau: vec![ItemInclus("Test Niveau".to_string())],
            position: vec![ItemInclus("Test Position".to_string())],
            echelon: vec![],
            categorie: vec![],
            coefficient: vec![],
            indice: vec![],
            degre: vec![],
            qualification: vec![],
            classe: vec![],
            palier: vec![],
            points: vec![],
            seuil: vec![],
            strate: vec![],
            groupe: vec![],
        });

        // When I check if an item is applicable
        let result = list.is_applicable(&Some(ClassificationContrat {
            niveau: Some("Test Niveau".to_string()),
            position: Some("Test Position".to_string()),
            echelon: None,
            categorie: None,
            coefficient: None,
            indice: None,
            degre: None,
            qualification: None,
            classe: None,
            palier: None,
            points: None,
            seuil: None,
            strate: None,
        }));

        assert_eq!(result, true);
    }
}
