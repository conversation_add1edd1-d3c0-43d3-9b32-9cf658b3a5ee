use std::fmt::{Display, Formatter};
use thiserror::Error;
use crate::dalloz_types::TempsTravailEffectifInputOutput;
use crate::read::commun::dalloz_rule_engine::rule::Rule;
use crate::read::decide_regles_absence::{Absence, Contrat, Etablissement};

pub trait TempsTravailEffectifQuery {
    async fn get_temps_travail_effectif(
        &self,
        id_cc: &i32,
    ) -> Result<Vec<TempsTravailEffectifInputOutput>, TempsTravailEffectifError>;
}

#[derive(Debug, Error)]
pub enum TempsTravailEffectifError {
    NotFound(String),
}

impl Display for TempsTravailEffectifError {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match self {
            TempsTravailEffectifError::NotFound(msg) => write!(f, "Not Found: {}", msg),
        }
    }
}

pub async fn is_temps_travail_effectif_us(
    absence: &Absence,
    contrat: &Contrat,
    etablissement: &Etablissement,
    temps_travail_effectif_query: &impl TempsTravailEffectifQuery,
) -> Result<bool, TempsTravailEffectifError> {
    let temps_travail_effectif = temps_travail_effectif_query
        .get_temps_travail_effectif(&contrat.id_convention)
        .await;

    let temps_travail_effectif = match temps_travail_effectif {
        Ok(temps_travail_effectif) => {
            if temps_travail_effectif.is_empty() {
                temps_travail_effectif_query
                    .get_temps_travail_effectif(&9999)
                    .await?
            } else {
                temps_travail_effectif
            }
        }
        Err(_) => {
            temps_travail_effectif_query
                .get_temps_travail_effectif(&9999)
                .await?
        }
    };

    for temps in temps_travail_effectif {
        let temps_travail_input = &temps.temps_travail_effectif_input;

        if !temps_travail_input
            .elargissement
            .is_applicable(&contrat.elargissement)
        {
            continue;
        }
        if !temps_travail_input
            .zones_geographiques
            .is_applicable(&etablissement.departement)
        {
            continue;
        }
        if !temps_travail_input
            .secteurs_activite
            .is_applicable(&contrat.secteur_activite)
        {
            continue;
        }
        if !temps_travail_input
            .adherent
            .is_applicable(&etablissement.organisme_signataire.is_some())
        {
            continue;
        }
        if !temps_travail_input
            .organisme_signataire
            .is_applicable(&etablissement.organisme_signataire)
        {
            continue;
        }
        if !temps_travail_input
            .date_validite
            .is_applicable(&absence.date_debut_absence)
        {
            continue;
        }
        if !temps_travail_input
            .categories_professionnelles
            .is_applicable(&contrat.categorie_professionnelle)
        {
            continue;
        }
        if !temps_travail_input.emploi.is_applicable(&contrat.emploi) {
            continue;
        }
        if !temps_travail_input
            .specificites_emploi
            .is_applicable(&contrat.specificite_emploi)
        {
            continue;
        }
        if !temps_travail_input
            .service_filiere
            .is_applicable(&contrat.service_filiere)
        {
            continue;
        }
        if !temps_travail_input
            .classification
            .is_applicable(&contrat.classification)
        {
            continue;
        }
        if !temps_travail_input
            .type_absence_sante
            .is_applicable(&absence.code_absence)
        {
            continue;
        }
        if !temps_travail_input
            .variable_client_absence
            .is_applicable(&absence.precision_type_absence)
        {
            continue;
        }
        if !temps_travail_input
            .anciennete
            .is_applicable(&contrat.anciennete_en_annee)
        {
            continue;
        }
        if !temps_travail_input
            .duree_absence
            .is_applicable(&absence.duree_en_jours)
        {
            continue;
        }
        if !temps_travail_input
            .nature_contrat
            .is_applicable(&contrat.nature)
        {
            continue;
        }
        if !temps_travail_input
            .motif_c_d_d
            .is_applicable(&contrat.motif_c_d_d)
        {
            continue;
        }

        if temps
            .temps_travail_effectif_output
            .temps_travail_effectif
            .is_applicable(&())
        {
            return Ok(true);
        }
    }

    Ok(false)
}
