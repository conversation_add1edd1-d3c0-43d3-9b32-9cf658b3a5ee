use crate::dalloz_types::{Convention, DroitsConfondus, DroitsConfondusInputOutput};
use crate::read::commun::classification_contrat::ClassificationContrat;
use crate::read::commun::dalloz_rule_engine::rule::Rule;
use chrono::NaiveDate;
use std::collections::HashSet;
use std::env::current_dir;
use std::fmt::{Display, Formatter};
use thiserror::Error;
use tokio::fs;
use crate::read::decide_regles_absence::{Absence, Contrat, Etablissement};

#[derive(Debug; )]
pub struct AbsencesDroitsConfondus {
    types_absences: Vec<String>,
}

pub trait DroitsConfondusQuery {
    async fn get_droits_confondus(
        &self,
        id_cc: i32,
    ) -> Result<Vec<DroitsConfondusInputOutput>, DroitConfonduError>;
}

#[derive(Debug, Error)]
pub enum DroitConfonduError {
    NotFound(String),
    NotThemeMaladieForConvention(String),
}

impl Display for DroitConfonduError {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match self {
            DroitConfonduError::NotFound(e) => write!(f, "Not found: {e}"),
            DroitConfonduError::NotThemeMaladieForConvention(e) => {
                write!(f, "{e}")
            }
        }
    }
}

impl Rule for DroitsConfondus {
    type InputType = ();

    fn is_applicable(&self, _: &Self::InputType) -> bool {
        match self.code.as_str() {
            "01" | "03" | "04" => true,
            _ => false,
        }
    }
}

pub async fn get_droits_confondus_us(
    absence: &Absence,
    contrat: &Contrat,
    etablissement: &Etablissement,
    repository: &impl DroitsConfondusQuery,
) -> Result<AbsencesDroitsConfondus, DroitConfonduError> {
    let droits_confondus = repository
        .get_droits_confondus(contrat.id_convention)
        .await;

    let droits_confondus = match droits_confondus {
        Ok(droits_confondus) => {
            if droits_confondus.is_empty() {
                repository.get_droits_confondus(9999).await?
            } else {
                droits_confondus
            }
        }
        Err(e) => match e {
            DroitConfonduError::NotFound(e) => {
                return Err(DroitConfonduError::NotFound(e));
            }
            DroitConfonduError::NotThemeMaladieForConvention(_) => {
                repository.get_droits_confondus(9999).await?
            }
        },
    };

    let mut types_absences = HashSet::new();
    types_absences.insert(absence.code_absence.clone());

    for droit in droits_confondus {
        if !droit
            .droits_confondus_input
            .date_validite
            .is_applicable(&absence.date_debut)
        {
            println!("Date validite non applicable for rule id {}", &droit.id);
            continue;
        }

        if !droit
            .droits_confondus_input
            .type_absence_sante
            .is_applicable(&absence.code_absence)
        {
            println!("Type absence non applicable for rule id {}", &droit.id);
            continue;
        }

        if !droit
            .droits_confondus_input
            .categories_professionnelles
            .is_applicable(&contrat.categorie_professionnelle)
        {
            println!(
                "Categorie professionnelle non applicable for rule id {}",
                &droit.id
            );
            continue;
        }

        if !droit
            .droits_confondus_input
            .emploi
            .is_applicable(&contrat.emploi)
        {
            println!("Emploi non applicable for rule id {}", &droit.id);
            continue;
        }

        if !droit
            .droits_confondus_input
            .secteurs_activite
            .is_applicable(&contrat.service_filiere)
        {
            println!("Secteur activite non applicable for rule id {}", &droit.id);
            continue;
        }

        if !droit
            .droits_confondus_input
            .organisme_signataire
            .is_applicable(&etablissement.organisme_signataire)
        {
            println!(
                "Organisme signataire non applicable for rule id {}",
                &droit.id
            );
            continue;
        }

        if !droit
            .droits_confondus_input
            .zones_geographiques
            .is_applicable(&etablissement.departement)
        {
            println!("Zone géographique non applicable for rule id {}", &droit.id);
            continue;
        }

        if !droit
            .droits_confondus_input
            .variable_client_absence
            .is_applicable(&absence.specificite_absence)
        {
            println!(
                "Variable client absence non applicable for rule id {}",
                &droit.id
            );
            continue;
        }

        if !droit
            .droits_confondus_output
            .droits_confondus
            .as_ref()
            .unwrap()
            .is_applicable(&())
        {
            println!("Pas de droits confondus applicable {}", &droit.id);
            continue;
        }

        types_absences.extend(
            droit
                .droits_confondus_input
                .type_absence_sante
                .iter()
                .map(|item| item.0.code.clone())
                .collect::<Vec<String>>(),
        );
    }

    Ok(AbsencesDroitsConfondus {
        types_absences: types_absences.into_iter().collect(),
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::NaiveDate;
    use crate::read::decide_regles_absence::IMReglesAbsenceQuery;

    fn init_test_data(id_convention: i32) -> (Absence, Etablissement, Contrat) {
        let absence = Absence {
            code_absence: "01".to_string(),
            precision_type_absence: None,
            date_debut_absence: Default::default(),
            duree_en_jours: 0.0,
            nombre_heures_hospitalisation: None,
            specificite_absence: None,
            date_debut: NaiveDate::from_ymd_opt(2025, 01, 01).unwrap(),
        };

        let etablissement = Etablissement {
            departement: "75".to_string(),
            organisme_signataire: None,
        };

        let contrat = Contrat {
            id_convention,
            emploi: "Employé".to_string(),
            specificite_emploi: None,
            categorie_professionnelle: "Employé".to_string(),
            service_filiere: None,
            nature: "CDI".to_string(),
            date_de_naissance: Default::default(),
            motif_c_d_d: None,
            classification: ClassificationContrat::default(),
            secteur_activite: None,
            elargissement: None,
            anciennete_en_annee: None,
            type_contrat_aide: None,
            specificite_contrat: None,
            departement_de_travail: "75".to_string(),
        };

        (absence, etablissement, contrat)
    }

    #[tokio::test]
    async fn it_should_manage_droits_confondus_for_convention() {
        // Given I'm looking to get the types absences in droit confondus
        let (absence, etablissement, contrat) = init_test_data(9999);

        // When I call the function
        let result =
            get_droits_confondus_us(&absence, &contrat, &etablissement, &IMReglesAbsenceQuery)
                .await;

        // Then I should get the list of types absences
        assert!(result.is_ok());
        let droits_confondus = result.unwrap();

        // Should at least contain the original absence type
        assert_eq!(droits_confondus.types_absences.len(), 6);
        assert!(droits_confondus.types_absences.contains(&"01".to_string()));
        assert!(droits_confondus.types_absences.contains(&"04".to_string()));
        assert!(droits_confondus.types_absences.contains(&"05".to_string()));
        assert!(droits_confondus.types_absences.contains(&"06".to_string()));
        assert!(droits_confondus.types_absences.contains(&"AB".to_string()));
        assert!(droits_confondus.types_absences.contains(&"07".to_string()));
    }

    #[tokio::test]
    async fn it_should_return_error_when_convention_not_found() {
        // Given a non-existent convention
        let (absence, etablissement, contrat) = init_test_data(12345);

        // When I call the function
        let result =
            get_droits_confondus_us(&absence, &contrat, &etablissement, &IMReglesAbsenceQuery)
                .await;

        // Then I should get an error
        assert!(result.is_err());
        match result.unwrap_err() {
            DroitConfonduError::NotFound(e) => {
                assert_eq!(e, "Convention not found: 12345".to_string());
            }
            _ => panic!("Expected NotThemeMaladieForConvention error"),
        }
    }

    #[tokio::test]
    async fn it_should_return_droit_commun_when_droits_confondus_is_empty() {
        let (absence, etablissement, contrat) = init_test_data(1261);
        // When I call the function
        let result =
            get_droits_confondus_us(&absence, &contrat, &etablissement, &IMReglesAbsenceQuery)
                .await;

        // Then I should get the list of types absences
        assert!(result.is_ok());
        let droits_confondus = result.unwrap();

        // Should at least contain the original absence type
        assert_eq!(droits_confondus.types_absences.len(), 5);
    }
}
