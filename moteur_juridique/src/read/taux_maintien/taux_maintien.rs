use std::fmt::{Display, Formatter};
use thiserror::Error;
use crate::calculus::calculate_exact_age_in_years;
use crate::dalloz_types::DroitAbsencesSanteInputOutput;
use crate::read::commun::dalloz_rule_engine::rule::Rule;
use crate::read::decide_regles_absence::{Absence, Contrat, Etablissement};

#[derive(Debug, Error)]
pub enum TauxMaintienError {
    NotFound(String),
    InputError(String),
}

impl Display for TauxMaintienError {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match self {
            TauxMaintienError::NotFound(msg) => write!(f, "Not Found: {}", msg),
            TauxMaintienError::InputError(msg) => write!(f, "Input Error: {}", msg),
        }
    }
}
pub type NombreJours = f64;
pub type Taux = f64;


pub struct TauxMaintien {
    pub taux1: Option<(NombreJours, Taux)>,
    pub taux2: Option<(NombreJours, Taux)>,
    pub taux3: Option<(NombreJours, Taux)>,
    pub taux4: Option<(NombreJours, Taux)>,
}

pub trait TauxMaintienQuery {
    async fn get_taux_maintien(
        &self,
        id_cc: i32,
    ) -> Result<Vec<DroitAbsencesSanteInputOutput>, TauxMaintienError>;
}

pub async fn get_taux_maintien_us(
    absence: &Absence,
    contrat: &Contrat,
    etablissement: &Etablissement,
    taux_maintien_repository: &impl TauxMaintienQuery,
) -> Result<Option<TauxMaintien>, TauxMaintienError> {
    let carences = taux_maintien_repository
        .get_taux_maintien(contrat.id_convention)
        .await?;

    for carence in carences {
        let input_rule = &carence.droit_absences_sante_input;

        if !input_rule
            .elargissement
            .is_applicable(&contrat.elargissement)
        {
            continue;
        }
        if !input_rule
            .zones_geographiques
            .is_applicable(&etablissement.departement)
        {
            continue;
        }
        if !input_rule
            .secteurs_activite
            .is_applicable(&contrat.secteur_activite)
        {
            continue;
        }
        if !input_rule
            .adherent
            .is_applicable(&etablissement.organisme_signataire.is_some())
        {
            continue;
        }
        if !input_rule
            .organisme_signataire
            .is_applicable(&etablissement.organisme_signataire)
        {
            continue;
        }
        if !input_rule
            .date_validite
            .is_applicable(&absence.date_debut_absence)
        {
            continue;
        }
        if !input_rule
            .categories_professionnelles
            .is_applicable(&contrat.categorie_professionnelle)
        {
            continue;
        }
        if !input_rule.emploi.is_applicable(&contrat.emploi) {
            continue;
        }
        if !input_rule
            .specificites_emploi
            .is_applicable(&contrat.specificite_emploi)
        {
            continue;
        }
        if !input_rule
            .secteurs_activite
            .is_applicable(&contrat.service_filiere)
        {
            continue;
        }
        if !input_rule
            .classification
            .is_applicable(&contrat.classification)
        {
            continue;
        }
        if !input_rule
            .type_absence_sante
            .is_applicable(&absence.code_absence)
        {
            continue;
        }
        if !input_rule
            .variable_client_absence
            .is_applicable(&absence.precision_type_absence)
        {
            continue;
        }
        if !input_rule
            .anciennete1
            .is_applicable(&contrat.anciennete_en_annee)
        {
            continue;
        }
        // if !input_rule.anciennete2.is_applicable() {
        //     continue;
        // }
        // if !input_rule.gestion_arret_passe.is_applicable() {
        //     continue;
        // }
        // if !input_rule.beneficiaire_organisme.is_applicable() {
        //     continue;
        // }
        if !input_rule
            .duree_absence
            .is_applicable(&absence.duree_en_jours)
        {
            continue;
        }
        let age_salarie =
            calculate_exact_age_in_years(&contrat.date_de_naissance, &absence.date_debut_absence);

        if age_salarie.is_err() {
            return Err(TauxMaintienError::InputError(age_salarie.err().unwrap()));
        }

        if !input_rule.age_salarie.is_applicable(&age_salarie.unwrap()) {
            continue;
        }
        if !input_rule
            .duree_hospitalisation
            .is_applicable(&absence.nombre_heures_hospitalisation)
        {
            continue;
        }

        if !input_rule.nature_contrat.is_applicable(&contrat.nature) {
            continue;
        }

        let output = carence.droit_absences_sante_output;
        return Ok(Some(TauxMaintien {
            taux1: Some((
                output.taux1.clone().unwrap_or_default().nb_jours.unwrap(),
                output.taux1.unwrap_or_default().taux.unwrap(),
            )),
            taux2: Some((
                output.taux2.clone().unwrap_or_default().nb_jours.unwrap(),
                output.taux2.unwrap_or_default().taux.unwrap(),
            )),
            taux3: Some((
                output.taux3.clone().unwrap_or_default().nb_jours.unwrap(),
                output.taux3.unwrap_or_default().taux.unwrap(),
            )),
            taux4: Some((
                output.taux4.clone().unwrap_or_default().nb_jours.unwrap(),
                output.taux4.unwrap_or_default().taux.unwrap(),
            )),
        }));
    }

    Ok(None)
}
