use std::fmt::Display;
use thiserror::Error;
use crate::calculus::calculate_exact_age_in_years;
use crate::dalloz_types::{CarenceInputOutput, Convention};
use crate::read::commun::dalloz_rule_engine::rule::Rule;
use crate::read::decide_regles_absence::{Absence, Contrat, Etablissement};

#[derive(Debug, Error)]
pub enum CarenceError {
    NotFound(String),
    InputError(String),
}

impl Display for CarenceError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CarenceError::NotFound(message) => f.write_fmt(format_args!("Not found: {message}")),
            CarenceError::InputError(message) => f.write_fmt(format_args!("Input error: {message}")),
        }
    }
}


pub struct Carence {
    pub nombre_jours_carence: f64,
}

pub trait CarenceQuery {
    async fn get_carence(
        &self,
        id_cc: i32,
    ) -> Result<Vec<CarenceInputOutput>, CarenceError>;
}

pub async fn get_carence_us(
    absence: &Absence,
    contrat: &Contrat,
    etablissement: &Etablissement,
    carence_repository: &impl CarenceQuery,
) -> Result<Option<Carence>, CarenceError> {
    let carences = carence_repository
        .get_carence(contrat.id_convention)
        .await?;

    for carence in carences {
        let input_rule = &carence.carence_input;

        if !input_rule
            .elargissement
            .is_applicable(&contrat.elargissement)
        {
            continue;
        }
        if !input_rule
            .zones_geographiques
            .is_applicable(&etablissement.departement)
        {
            continue;
        }
        if !input_rule
            .secteurs_activite
            .is_applicable(&contrat.secteur_activite)
        {
            continue;
        }
        if !input_rule
            .adherent
            .is_applicable(&etablissement.organisme_signataire.is_some())
        {
            continue;
        }
        if !input_rule
            .organisme_signataire
            .is_applicable(&etablissement.organisme_signataire)
        {
            continue;
        }
        if !input_rule
            .date_validite
            .is_applicable(&absence.date_debut_absence)
        {
            continue;
        }
        if !input_rule
            .categories_professionnelles
            .is_applicable(&contrat.categorie_professionnelle)
        {
            continue;
        }
        if !input_rule.emploi.is_applicable(&contrat.emploi) {
            continue;
        }
        if !input_rule
            .specificites_emploi
            .is_applicable(&contrat.specificite_emploi)
        {
            continue;
        }
        if !input_rule
            .secteurs_activite
            .is_applicable(&contrat.service_filiere)
        {
            continue;
        }
        if !input_rule
            .classification
            .is_applicable(&contrat.classification)
        {
            continue;
        }
        if !input_rule
            .type_absence_sante
            .is_applicable(&absence.code_absence)
        {
            continue;
        }
        if !input_rule
            .variable_client_absence
            .is_applicable(&absence.precision_type_absence)
        {
            continue;
        }
        if !input_rule
            .anciennete1
            .is_applicable(&contrat.anciennete_en_annee)
        {
            continue;
        }
        // if !input_rule.anciennete2.is_applicable() {
        //     continue;
        // }
        // if !input_rule.gestion_arret_passe.is_applicable() {
        //     continue;
        // }
        // if !input_rule.beneficiaire_organisme.is_applicable() {
        //     continue;
        // }
        // if !input_rule.comparaison_divers.is_applicable() {
        //     continue;
        // }
        if !input_rule
            .duree_absence
            .is_applicable(&absence.duree_en_jours)
        {
            continue;
        }
        let age_salarie =
            calculate_exact_age_in_years(&contrat.date_de_naissance, &absence.date_debut_absence);

        if age_salarie.is_err() {
            return Err(CarenceError::InputError(
                age_salarie.err().unwrap(),
            ));
        }

        if !input_rule.age_salarie.is_applicable(&age_salarie.unwrap()) {
            continue;
        }
        if !input_rule
            .duree_hospitalisation
            .is_applicable(&absence.nombre_heures_hospitalisation)
        {
            continue;
        }

        if !input_rule.nature_contrat.is_applicable(&contrat.nature) {
            continue;
        }

        let output = carence.carence_output;
        return Ok(Some(Carence {
            nombre_jours_carence: output
                .droits_carence
                .unwrap_or_default()
                .nb_jours
                .unwrap_or_default(),
        }));
    }

    Ok(None)
}
