use std::env::current_dir;
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON>atter};
use chrono::NaiveDate;
use serde::Deserialize;
use tokio::fs;
use thiserror::Error;
use crate::dalloz_types::{CarenceInputOutput, Convention, DroitAbsencesSanteInputOutput, DroitsConfondusInputOutput, ReglesMaladieInputOutput, TempsTravailEffectifInputOutput};
use crate::read::carence::carence::{get_carence_us, Carence, CarenceError, CarenceQuery};
use crate::read::commun::classification_contrat::ClassificationContrat;
use crate::read::droits_confondu::droits_confondu::{get_droits_confondus_us, AbsencesDroitsConfondus, DroitConfonduError, DroitsConfondusQuery};
use crate::read::regles_maladie::regles_maladie::{get_regles_maladie_us, <PERSON>lesCalculMaladie, ReglesMaladieError, ReglesMaladieQuery};
use crate::read::taux_maintien::taux_maintien::{get_taux_maintien_us, TauxMaintien, TauxMaintienError, TauxMaintienQuery};
use crate::read::temps_travail_effectif::temps_travail_effectif::{is_temps_travail_effectif_us, TempsTravailEffectifError, TempsTravailEffectifQuery};

trait ReglesAbsenceQuery: ReglesMaladieQuery + CarenceQuery + DroitsConfondusQuery + TempsTravailEffectifQuery + TauxMaintienQuery + Send + Sync {}

pub struct IMReglesAbsenceQuery;

impl ReglesMaladieQuery for IMReglesAbsenceQuery {
    async fn get_regles_maladie(
        &self,
        id_cc: i32,
    ) -> Result<Vec<ReglesMaladieInputOutput>, ReglesMaladieError> {
        let mut convention_dir = current_dir().unwrap();

        convention_dir.push(format!("resources/conventions/cc_{}.json", id_cc));

        let convention: Convention =
            serde_json::from_str(&fs::read_to_string(convention_dir).await.map_err(|_| {
                ReglesMaladieError::NotFound(format!("Convention not found: {}", id_cc))
            })?)
                .unwrap();

        let maladie_theme = convention.maladie_theme.ok_or_else(|| {
            ReglesMaladieError::NotFound(format!(
                "Règles maladie theme not found for convention: {}",
                id_cc
            ))
        })?;

        Ok(maladie_theme.regles_maladie)
    }
}
impl TempsTravailEffectifQuery for IMReglesAbsenceQuery {
    async fn get_temps_travail_effectif(&self, id_cc: &i32) -> Result<Vec<TempsTravailEffectifInputOutput>, TempsTravailEffectifError> {
        let mut convention_dir = current_dir().unwrap();

        convention_dir.push(format!("resources/conventions/cc_{}.json", id_cc));

        let convention: Convention =
            serde_json::from_str(&fs::read_to_string(convention_dir).await.map_err(|_| {
                TempsTravailEffectifError::NotFound(format!("Convention not found: {}", id_cc))
            })?)
                .unwrap();

        let maladie_theme = convention.maladie_theme.ok_or_else(|| {
            TempsTravailEffectifError::NotFound(format!(
                "Temps travail effectif not found for convention: {}",
                id_cc
            ))
        })?;

        Ok(maladie_theme.temps_travail_effectif)
    }
}
impl CarenceQuery for IMReglesAbsenceQuery {
    async fn get_carence(&self, id_cc: i32) -> Result<Vec<CarenceInputOutput>, CarenceError> {
        let mut convention_dir = current_dir().unwrap();

        convention_dir.push(format!("resources/conventions/cc_{}.json", id_cc));

        let convention: Convention =
            serde_json::from_str(&fs::read_to_string(convention_dir).await.map_err(|_| {
                CarenceError::NotFound(format!("Convention not found: {}", id_cc))
            })?)
                .unwrap();

        let maladie_theme = convention.maladie_theme.ok_or_else(|| {
            CarenceError::NotFound(format!(
                "Carence theme not found for convention: {}",
                id_cc
            ))
        })?;

        Ok(maladie_theme.carence)
    }
}
impl TauxMaintienQuery for IMReglesAbsenceQuery {
    async fn get_taux_maintien(&self, id_cc: i32) -> Result<Vec<DroitAbsencesSanteInputOutput>, TauxMaintienError> {
        let mut convention_dir = current_dir().unwrap();

        convention_dir.push(format!("resources/conventions/cc_{}.json", id_cc));

        let convention: Convention =
            serde_json::from_str(&fs::read_to_string(convention_dir).await.map_err(|_| {
                TauxMaintienError::NotFound(format!("Convention not found: {}", id_cc))
            })?)
                .unwrap();

        let maladie_theme = convention.maladie_theme.ok_or_else(|| {
            TauxMaintienError::NotFound(format!(
                "Taux maintien not found for convention: {}",
                id_cc
            ))
        })?;

        Ok(maladie_theme.droit_absences_sante)
    }
}
impl DroitsConfondusQuery for IMReglesAbsenceQuery {
    async fn get_droits_confondus(&self, id_cc: i32) -> Result<Vec<DroitsConfondusInputOutput>, DroitConfonduError> {
        let mut convention_dir = current_dir().unwrap();

        convention_dir.push(format!("resources/conventions/cc_{}.json", id_cc));

        let convention: Convention =
            serde_json::from_str(&fs::read_to_string(convention_dir).await.map_err(|_| {
                DroitConfonduError::NotFound(format!("Convention not found: {}", id_cc))
            })?)
                .unwrap();

        let maladie_theme = convention.maladie_theme.ok_or_else(|| {
            DroitConfonduError::NotThemeMaladieForConvention(format!(
                "Droits confondu theme not found for convention: {}",
                id_cc
            ))
        })?;

        Ok(maladie_theme.droits_confondus)
    }
}

pub struct Absence {
    pub date_debut: NaiveDate,
    pub code_absence: String,
    pub precision_type_absence: Option<String>,
    pub date_debut_absence: NaiveDate,
    pub duree_en_jours: f64,
    pub nombre_heures_hospitalisation: Option<f64>,
    pub specificite_absence: Option<String>,
}

pub struct Contrat {
    pub id_convention: i32,
    pub categorie_professionnelle: String,
    pub emploi: String,
    pub secteur_activite: Option<String>,
    pub classification: ClassificationContrat,
    pub elargissement: Option<String>,
    pub specificite_emploi: Option<String>,
    pub service_filiere: Option<String>,
    pub anciennete_en_annee: Option<f64>,
    pub specificite_contrat: Option<String>,
    pub nature: String,
    pub date_de_naissance: NaiveDate,
    pub motif_c_d_d: Option<String>,
    pub type_contrat_aide: Option<String>,
    pub departement_de_travail: String,
}

pub struct Etablissement {
    pub departement: String,
    pub organisme_signataire: Option<String>,
}

#[derive(Debug, Error)]
enum DecisionMaladieError {
    CarenceError(#[from] CarenceError),
    DroitsConfondusError(#[from] DroitConfonduError),
    ReglesMaladieError(#[from] ReglesMaladieError),
    TauxMaintienError(#[from] TauxMaintienError),
    TempsTravailEffectifError(#[from] TempsTravailEffectifError),
}

impl Display for DecisionMaladieError {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match self {
            DecisionMaladieError::CarenceError(e) => write!(f, "CarenceError: {}", e),
            DecisionMaladieError::DroitsConfondusError(e) => write!(f, "DroitsConfondusError: {}", e),
            DecisionMaladieError::ReglesMaladieError(e) => write!(f, "ReglesMaladieError: {}", e),
            DecisionMaladieError::TauxMaintienError(e) => write!(f, "TauxMaintienError: {}", e),
            DecisionMaladieError::TempsTravailEffectifError(e) => write!(f, "TempsTravailEffectifError: {}", e),
        }
    }
}

#[derive(Debug, Deserialize)]
struct DecisionMaladie {
    carence: Option<Carence>,
    droits_confondus: AbsencesDroitsConfondus,
    regles_maladie: Option<ReglesCalculMaladie>,
    taux_maintien: Option<TauxMaintien>,
    temps_travail_effectif: bool,
}
pub async fn decide_regles_absence(
    absence: &Absence,
    contrat: &Contrat,
    etablissement: &Etablissement,
    queries: &impl ReglesAbsenceQuery,
) -> Result<DecisionMaladie, DecisionMaladieError>{
    let regles_maladie_use_case = get_regles_maladie_us(
        &absence,
        &contrat,
        &etablissement,
        queries,
    ).await?;

    let taux_maintien_use_case = get_taux_maintien_us(
        &absence,
        &contrat,
        &etablissement,
        queries,
    ).await?;

    let carence_use_case = get_carence_us(
        &absence,
        &contrat,
        &etablissement,
        queries,
    ).await?;

    let temps_travail_effectif_use_case = is_temps_travail_effectif_us(
        &absence,
        &contrat,
        &etablissement,
        queries,
    ).await?;

    let droits_confondus_use_case = get_droits_confondus_us(
        &absence,
        &contrat,
        &etablissement,
        queries,
    ).await?;


    Ok(DecisionMaladie {
        carence: carence_use_case,
        droits_confondus: droits_confondus_use_case,
        regles_maladie: regles_maladie_use_case,
        taux_maintien: taux_maintien_use_case,
        temps_travail_effectif: temps_travail_effectif_use_case,
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::NaiveDate;
    use std::collections::HashMap;

    // Mock implementation for testing
    pub struct MockReglesAbsenceQuery {
        pub regles_maladie_data: HashMap<i32, Vec<ReglesMaladieInputOutput>>,
        pub taux_maintien_data: HashMap<i32, Vec<DroitAbsencesSanteInputOutput>>,
        pub carence_data: HashMap<i32, Vec<CarenceInputOutput>>,
        pub temps_travail_effectif_data: HashMap<i32, Vec<TempsTravailEffectifInputOutput>>,
        pub droits_confondus_data: HashMap<i32, Vec<DroitsConfondusInputOutput>>,
    }

    impl MockReglesAbsenceQuery {
        pub fn new() -> Self {
            Self {
                regles_maladie_data: HashMap::new(),
                taux_maintien_data: HashMap::new(),
                carence_data: HashMap::new(),
                temps_travail_effectif_data: HashMap::new(),
                droits_confondus_data: HashMap::new(),
            }
        }

        pub fn with_regles_maladie(mut self, id_cc: i32, data: Vec<ReglesMaladieInputOutput>) -> Self {
            self.regles_maladie_data.insert(id_cc, data);
            self
        }

        pub fn with_taux_maintien(mut self, id_cc: i32, data: Vec<DroitAbsencesSanteInputOutput>) -> Self {
            self.taux_maintien_data.insert(id_cc, data);
            self
        }

        pub fn with_carence(mut self, id_cc: i32, data: Vec<CarenceInputOutput>) -> Self {
            self.carence_data.insert(id_cc, data);
            self
        }

        pub fn with_temps_travail_effectif(mut self, id_cc: i32, data: Vec<TempsTravailEffectifInputOutput>) -> Self {
            self.temps_travail_effectif_data.insert(id_cc, data);
            self
        }

        pub fn with_droits_confondus(mut self, id_cc: i32, data: Vec<DroitsConfondusInputOutput>) -> Self {
            self.droits_confondus_data.insert(id_cc, data);
            self
        }
    }

    impl ReglesMaladieQuery for MockReglesAbsenceQuery {
        async fn get_regles_maladie(&self, id_cc: i32) -> Result<Vec<ReglesMaladieInputOutput>, ReglesMaladieError> {
            self.regles_maladie_data
                .get(&id_cc)
                .cloned()
                .ok_or_else(|| ReglesMaladieError::NotFound(format!("Convention not found: {}", id_cc)))
        }
    }

    impl TauxMaintienQuery for MockReglesAbsenceQuery {
        async fn get_taux_maintien(&self, id_cc: i32) -> Result<Vec<DroitAbsencesSanteInputOutput>, TauxMaintienError> {
            self.taux_maintien_data
                .get(&id_cc)
                .cloned()
                .ok_or_else(|| TauxMaintienError::NotFound(format!("Convention not found: {}", id_cc)))
        }
    }

    impl CarenceQuery for MockReglesAbsenceQuery {
        async fn get_carence(&self, id_cc: i32) -> Result<Vec<CarenceInputOutput>, CarenceError> {
            self.carence_data
                .get(&id_cc)
                .cloned()
                .ok_or_else(|| CarenceError::NotFound(format!("Convention not found: {}", id_cc)))
        }
    }

    impl TempsTravailEffectifQuery for MockReglesAbsenceQuery {
        async fn get_temps_travail_effectif(&self, id_cc: &i32) -> Result<Vec<TempsTravailEffectifInputOutput>, TempsTravailEffectifError> {
            self.temps_travail_effectif_data
                .get(id_cc)
                .cloned()
                .ok_or_else(|| TempsTravailEffectifError::NotFound(format!("Convention not found: {}", id_cc)))
        }
    }

    impl DroitsConfondusQuery for MockReglesAbsenceQuery {
        async fn get_droits_confondus(&self, id_cc: i32) -> Result<Vec<DroitsConfondusInputOutput>, DroitConfonduError> {
            self.droits_confondus_data
                .get(&id_cc)
                .cloned()
                .ok_or_else(|| DroitConfonduError::NotFound(format!("Convention not found: {}", id_cc)))
        }
    }

    impl ReglesAbsenceQuery for MockReglesAbsenceQuery {}

    // Helper function to create test data
    fn create_test_absence() -> Absence {
        Absence {
            date_debut: NaiveDate::from_ymd_opt(2024, 1, 15).unwrap(),
            code_absence: "01".to_string(),
            precision_type_absence: Some("maladie".to_string()),
            date_debut_absence: NaiveDate::from_ymd_opt(2024, 1, 15).unwrap(),
            duree_en_jours: 5.0,
            nombre_heures_hospitalisation: None,
            specificite_absence: None,
        }
    }

    fn create_test_contrat() -> Contrat {
        Contrat {
            id_convention: 1486,
            categorie_professionnelle: "Employe".to_string(),
            emploi: "Développeur".to_string(),
            secteur_activite: Some("Informatique".to_string()),
            classification: ClassificationContrat::default(),
            elargissement: None,
            specificite_emploi: None,
            service_filiere: None,
            anciennete_en_annee: Some(2.0),
            specificite_contrat: None,
            nature: "CDI".to_string(),
            date_de_naissance: NaiveDate::from_ymd_opt(1990, 5, 15).unwrap(),
            motif_c_d_d: None,
            type_contrat_aide: None,
            departement_de_travail: "75".to_string(),
        }
    }

    fn create_test_etablissement() -> Etablissement {
        Etablissement {
            departement: "75".to_string(),
            organisme_signataire: Some("OPCO".to_string()),
        }
    }
