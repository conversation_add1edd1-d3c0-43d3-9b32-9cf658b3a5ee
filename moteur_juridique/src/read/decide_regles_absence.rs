use std::env::current_dir;
use std::fmt::{Disp<PERSON>, Formatter};
use chrono::NaiveDate;
use tokio::fs;
use thiserror::Error;
use crate::dalloz_types::{CarenceInputOutput, Convention, DroitAbsencesSanteInputOutput, DroitsConfondusInputOutput, ReglesMaladieInputOutput, TempsTravailEffectifInputOutput};
use crate::read::carence::carence::{get_carence_us, Carence, CarenceError, CarenceQuery};
use crate::read::commun::classification_contrat::ClassificationContrat;
use crate::read::droits_confondu::droits_confondu::{get_droits_confondus_us, AbsencesDroitsConfondus, DroitConfonduError, DroitsConfondusQuery};
use crate::read::regles_maladie::regles_maladie::{get_regles_maladie_us, <PERSON>lesCalculMaladie, <PERSON>lesMaladieError, ReglesMaladieQuery};
use crate::read::taux_maintien::taux_maintien::{get_taux_maintien_us, TauxMaintien, TauxMaintienError, TauxMaintienQuery};
use crate::read::temps_travail_effectif::temps_travail_effectif::{is_temps_travail_effectif_us, TempsTravailEffectifError, TempsTravailEffectifQuery};

trait ReglesAbsenceQuery: ReglesMaladieQuery + CarenceQuery + DroitsConfondusQuery + TempsTravailEffectifQuery + TauxMaintienQuery + Send + Sync {}

pub struct IMReglesAbsenceQuery;

impl ReglesMaladieQuery for IMReglesAbsenceQuery {
    async fn get_regles_maladie(
        &self,
        id_cc: i32,
    ) -> Result<Vec<ReglesMaladieInputOutput>, ReglesMaladieError> {
        let mut convention_dir = current_dir().unwrap();

        convention_dir.push(format!("resources/conventions/cc_{}.json", id_cc));

        let convention: Convention =
            serde_json::from_str(&fs::read_to_string(convention_dir).await.map_err(|_| {
                ReglesMaladieError::NotFound(format!("Convention not found: {}", id_cc))
            })?)
                .unwrap();

        let maladie_theme = convention.maladie_theme.ok_or_else(|| {
            ReglesMaladieError::NotFound(format!(
                "Règles maladie theme not found for convention: {}",
                id_cc
            ))
        })?;

        Ok(maladie_theme.regles_maladie)
    }
}
impl TempsTravailEffectifQuery for IMReglesAbsenceQuery {
    async fn get_temps_travail_effectif(&self, id_cc: &i32) -> Result<Vec<TempsTravailEffectifInputOutput>, TempsTravailEffectifError> {
        let mut convention_dir = current_dir().unwrap();

        convention_dir.push(format!("resources/conventions/cc_{}.json", id_cc));

        let convention: Convention =
            serde_json::from_str(&fs::read_to_string(convention_dir).await.map_err(|_| {
                TempsTravailEffectifError::NotFound(format!("Convention not found: {}", id_cc))
            })?)
                .unwrap();

        let maladie_theme = convention.maladie_theme.ok_or_else(|| {
            TempsTravailEffectifError::NotFound(format!(
                "Temps travail effectif not found for convention: {}",
                id_cc
            ))
        })?;

        Ok(maladie_theme.temps_travail_effectif)
    }
}
impl CarenceQuery for IMReglesAbsenceQuery {
    async fn get_carence(&self, id_cc: i32) -> Result<Vec<CarenceInputOutput>, CarenceError> {
        let mut convention_dir = current_dir().unwrap();

        convention_dir.push(format!("resources/conventions/cc_{}.json", id_cc));

        let convention: Convention =
            serde_json::from_str(&fs::read_to_string(convention_dir).await.map_err(|_| {
                CarenceError::NotFound(format!("Convention not found: {}", id_cc))
            })?)
                .unwrap();

        let maladie_theme = convention.maladie_theme.ok_or_else(|| {
            CarenceError::NotFound(format!(
                "Carence theme not found for convention: {}",
                id_cc
            ))
        })?;

        Ok(maladie_theme.carence)
    }
}
impl TauxMaintienQuery for IMReglesAbsenceQuery {
    async fn get_taux_maintien(&self, id_cc: i32) -> Result<Vec<DroitAbsencesSanteInputOutput>, TauxMaintienError> {
        let mut convention_dir = current_dir().unwrap();

        convention_dir.push(format!("resources/conventions/cc_{}.json", id_cc));

        let convention: Convention =
            serde_json::from_str(&fs::read_to_string(convention_dir).await.map_err(|_| {
                TauxMaintienError::NotFound(format!("Convention not found: {}", id_cc))
            })?)
                .unwrap();

        let maladie_theme = convention.maladie_theme.ok_or_else(|| {
            TauxMaintienError::NotFound(format!(
                "Taux maintien not found for convention: {}",
                id_cc
            ))
        })?;

        Ok(maladie_theme.droit_absences_sante)
    }
}
impl DroitsConfondusQuery for IMReglesAbsenceQuery {
    async fn get_droits_confondus(&self, id_cc: i32) -> Result<Vec<DroitsConfondusInputOutput>, DroitConfonduError> {
        let mut convention_dir = current_dir().unwrap();

        convention_dir.push(format!("resources/conventions/cc_{}.json", id_cc));

        let convention: Convention =
            serde_json::from_str(&fs::read_to_string(convention_dir).await.map_err(|_| {
                DroitConfonduError::NotFound(format!("Convention not found: {}", id_cc))
            })?)
                .unwrap();

        let maladie_theme = convention.maladie_theme.ok_or_else(|| {
            DroitConfonduError::NotThemeMaladieForConvention(format!(
                "Droits confondu theme not found for convention: {}",
                id_cc
            ))
        })?;

        Ok(maladie_theme.droits_confondus)
    }
}

pub struct Absence {
    pub date_debut: NaiveDate,
    pub code_absence: String,
    pub precision_type_absence: Option<String>,
    pub date_debut_absence: NaiveDate,
    pub duree_en_jours: f64,
    pub nombre_heures_hospitalisation: Option<f64>,
    pub specificite_absence: Option<String>,
}

pub struct Contrat {
    pub id_convention: i32,
    pub categorie_professionnelle: String,
    pub emploi: String,
    pub secteur_activite: Option<String>,
    pub classification: ClassificationContrat,
    pub elargissement: Option<String>,
    pub specificite_emploi: Option<String>,
    pub service_filiere: Option<String>,
    pub anciennete_en_annee: Option<f64>,
    pub specificite_contrat: Option<String>,
    pub nature: String,
    pub date_de_naissance: NaiveDate,
    pub motif_c_d_d: Option<String>,
    pub type_contrat_aide: Option<String>,
    pub departement_de_travail: String,
}

pub struct Etablissement {
    pub departement: String,
    pub organisme_signataire: Option<String>,
}

#[derive(Debug, Error)]
enum DecisionMaladieError {
    CarenceError(#[from] CarenceError),
    DroitsConfondusError(#[from] DroitConfonduError),
    ReglesMaladieError(#[from] ReglesMaladieError),
    TauxMaintienError(#[from] TauxMaintienError),
    TempsTravailEffectifError(#[from] TempsTravailEffectifError),
}

impl Display for DecisionMaladieError {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match self {
            DecisionMaladieError::CarenceError(e) => write!(f, "CarenceError: {}", e),
            DecisionMaladieError::DroitsConfondusError(e) => write!(f, "DroitsConfondusError: {}", e),
            DecisionMaladieError::ReglesMaladieError(e) => write!(f, "ReglesMaladieError: {}", e),
            DecisionMaladieError::TauxMaintienError(e) => write!(f, "TauxMaintienError: {}", e),
            DecisionMaladieError::TempsTravailEffectifError(e) => write!(f, "TempsTravailEffectifError: {}", e),
        }
    }
}


struct DecisionMaladie {
    carence: Option<Carence>,
    droits_confondus: AbsencesDroitsConfondus,
    regles_maladie: Option<ReglesCalculMaladie>,
    taux_maintien: Option<TauxMaintien>,
    temps_travail_effectif: bool,
}
pub async fn decide_regles_absence(
    absence: &Absence,
    contrat: &Contrat,
    etablissement: &Etablissement,
    queries: &impl ReglesAbsenceQuery,
) -> Result<DecisionMaladie, DecisionMaladieError>{
    let query = IMReglesAbsenceQuery {};

    let regles_maladie_use_case = get_regles_maladie_us(
        &absence,
        &contrat,
        &etablissement,
        &query,
    ).await?;

    let taux_maintien_use_case = get_taux_maintien_us(
        &absence,
        &contrat,
        &etablissement,
        &query,
    ).await?;

    let carence_use_case = get_carence_us(
        &absence,
        &contrat,
        &etablissement,
        &query,
    ).await?;

    let temps_travail_effectif_use_case = is_temps_travail_effectif_us(
        &absence,
        &contrat,
        &etablissement,
        &query,
    ).await?;

    let droits_confondus_use_case = get_droits_confondus_us(
        &absence,
        &contrat,
        &etablissement,
        &query,
    ).await?;


    Ok(DecisionMaladie {
        carence: carence_use_case,
        droits_confondus: droits_confondus_use_case,
        regles_maladie: regles_maladie_use_case,
        taux_maintien: taux_maintien_use_case,
        temps_travail_effectif: temps_travail_effectif_use_case,
    })
}
