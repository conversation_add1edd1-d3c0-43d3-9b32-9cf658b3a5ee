use std::fmt::Display;
use thiserror::Error;
use crate::dalloz_types::ReglesMaladieInputOutput;
use crate::read::commun::dalloz_rule_engine::rule::Rule;
use crate::read::decide_regles_absence::{Absence, Contrat, Etablissement};


pub struct ReglesCalculMaladie {
    pub base_calcul_maintien: String,
    pub impact_csg: String,
    pub periode_calcul_indemnisation: String,
    pub plafond_indemnisation: String,
    pub maintien_du_salaire_net: bool,
}

#[derive(Debug, Error)]
pub enum ReglesMaladieError {
    NotFound(String),
}

impl Display for ReglesMaladieError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ReglesMaladieError::NotFound(msg) => write!(f, "Not Found: {}", msg),
        }
    }
}

pub trait ReglesMaladieQuery {
    async fn get_regles_maladie(
        &self,
        id_cc: i32,
    ) -> Result<Vec<ReglesMaladieInputOutput>, ReglesMaladieError>;
}

pub async fn get_regles_maladie_us(
    absence: &Absence,
    contrat: &Contrat,
    etablissement: &Etablissement,
    repository: &impl ReglesMaladieQuery,
) -> Result<Option<ReglesCalculMaladie>, ReglesMaladieError> {
    let regles_maladie = repository
        .get_regles_maladie(contrat.id_convention)
        .await;

    let regles_maladie = match regles_maladie {
        Ok(carences) => {
            if carences.is_empty() {
                repository.get_regles_maladie(9999).await?
            } else {
                carences
            }
        }
        Err(_) => repository.get_regles_maladie(9999).await?,
    };

    for regle_maladie in regles_maladie {
        let input_rule = &regle_maladie.regles_maladie_input;

        if !input_rule.date_validite.is_applicable(&absence.date_debut_absence) {
            println!("Date validite non applicable for rule id {}", &regle_maladie.id);
            continue;
        }

        if !input_rule.type_absence_sante.is_applicable(&absence.code_absence) {
            println!(
                "Type absence sante non applicable for rule id {}",
                &regle_maladie.id
            );
            continue;
        }

        if !input_rule
            .variable_client_absence
            .is_applicable(&absence.precision_type_absence)
        {
            println!(
                "Variable client absence non applicable for rule id {}",
                &regle_maladie.id
            );
            continue;
        }

        if !input_rule
            .zones_geographiques
            .is_applicable(&etablissement.departement)
        {
            println!(
                "Zone geographique non applicable for rule id {}",
                &regle_maladie.id
            );
            continue;
        }

        if !input_rule
            .categories_professionnelles
            .is_applicable(&contrat.categorie_professionnelle)
        {
            println!(
                "Categorie professionnelle non applicable for rule id {}",
                &regle_maladie.id
            );
            continue;
        }

        if input_rule.adherent.is_applicable(&etablissement.organisme_signataire.is_some())
            && input_rule.organisme_signataire.is_applicable(&etablissement.organisme_signataire)
        {
            println!("Adherent non applicable for rule id {}", &regle_maladie.id);
            continue;
        }

        if !input_rule.emploi.is_applicable(&contrat.emploi) {
            println!("emploi non applicable for rule id {}", &regle_maladie.id);
            continue;
        }

        if !input_rule
            .secteurs_activite
            .is_applicable(&contrat.service_filiere)
        {
            println!("Secteur activite non applicable for rule id {}", &regle_maladie.id);
            continue;
        }

        let output = regle_maladie.regles_maladie_output.base_indemnisation.unwrap_or_default();

        return Ok(Some(ReglesCalculMaladie {
            base_calcul_maintien: output.base_calcul_indemnisation.unwrap_or_default().libelle.clone(),
            periode_calcul_indemnisation: output.periode_calcul_indemnisation.unwrap_or_default().libelle.clone(),
            plafond_indemnisation: output.limite_base_indemnisation.unwrap_or_default().libelle.clone(),
            impact_csg: regle_maladie.regles_maladie_output.impact_c_s_g.unwrap_or_default().libelle,
            maintien_du_salaire_net: regle_maladie.regles_maladie_output.maintien_salaire_net.is_applicable(&()),
        }));

    }

    Ok(None)
}
